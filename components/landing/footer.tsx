import React from "react";
import Link from "next/link";
import { Github, Twitter, Mail, Code, ExternalLink, User } from "lucide-react";
import Image from "next/image";

export function LandingFooter() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-backgroundSecondary border-t border-borderPrimary/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="flex flex-col md:flex-row items-center justify-between gap-6">
          {/* Brand section */}
          <div className="flex items-center gap-3">
            <Link
              href="/"
              className="flex items-center gap-2 relative z-10"
            >
              {/* <div className="w-8 h-8 lg:w-10 lg:h-10 rounded-xl bg-gradient-to-br from-brandPrimary to-brandSecondary flex items-center justify-center">
              <span className="text-white font-bold text-sm lg:text-base">
                P
              </span>
            </div> */}
              <Image src="/icon.svg" alt="logo" width={24} height={24} />
              <span className="font-bold text-xl  gradient-text">
                Profolify
              </span>
            </Link>
          </div>

          {/* Links */}
          <div className="flex items-center gap-6 text-sm">
            <Link href="#features" className="text-textSecondary hover:text-brandPrimary transition-colors duration-200">
              Features
            </Link>
            <Link href="#themes" className="text-textSecondary hover:text-brandPrimary transition-colors duration-200">
              Themes
            </Link>
            <Link href="#pricing" className="text-textSecondary hover:text-brandPrimary transition-colors duration-200">
              Pricing
            </Link>
          </div>

          {/* Social links */}
          <div className="flex items-center gap-3">
            <Link
              href="https://github.com/darshanbajgain"
              className="w-8 h-8 rounded-lg bg-backgroundPrimary/50 border border-borderPrimary/50 flex items-center justify-center text-textSecondary hover:text-brandPrimary hover:border-brandPrimary/50 transition-colors duration-200"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="GitHub"
            >
              <Github className="w-4 h-4" />
            </Link>
            <Link
              href="https://x.com/darshan_bajgain"
              className="w-8 h-8 rounded-lg bg-backgroundPrimary/50 border border-borderPrimary/50 flex items-center justify-center text-textSecondary hover:text-brandPrimary hover:border-brandPrimary/50 transition-colors duration-200"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="Twitter"
            >
              <Twitter className="w-4 h-4" />
            </Link>
            <Link
              href="mailto:<EMAIL>"
              className="w-8 h-8 rounded-lg bg-backgroundPrimary/50 border border-borderPrimary/50 flex items-center justify-center text-textSecondary hover:text-brandPrimary hover:border-brandPrimary/50 transition-colors duration-200"
              aria-label="Email"
            >
              <Mail className="w-4 h-4" />
            </Link>
          </div>
        </div>

        {/* Meet the Creator Section */}
        <div className="mt-8 pt-6 border-t border-borderPrimary/30">
          <div className="flex flex-col lg:flex-row items-center justify-between gap-6">
            {/* Copyright */}
            <p className="text-sm text-textSecondary">© {currentYear} Profolify. All rights reserved.</p>

            {/* Developer Showcase */}
            <div className="flex flex-col sm:flex-row items-center gap-4">
              {/* Meet the Creator Badge */}
              <div className="flex items-center gap-2 text-sm text-textSecondary">
                <User className="w-4 h-4 text-brandPrimary" />
                <span>Meet the Creator</span>
              </div>

              {/* Developer Info Card */}
              <div className="flex items-center gap-3 p-3 rounded-xl bg-gradient-to-r from-brandPrimary/5 to-brandSecondary/5 border border-brandPrimary/20 backdrop-blur-sm">
                {/* Developer Avatar/Icon */}
                <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-brandPrimary to-brandSecondary flex items-center justify-center shadow-lg">
                  <Code className="w-5 h-5 text-white" />
                </div>

                {/* Developer Details */}
                <div className="flex flex-col">
                  <span className="font-semibold text-textPrimary text-sm">Darshan Bajgain</span>
                  <span className="text-xs text-textSecondary">Full Stack Developer</span>
                </div>

                {/* Action Links */}
                <div className="flex items-center gap-2 ml-2">
                  <a
                    href="https://darshanbajgain.com.np/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="group flex items-center gap-1 px-2 py-1 rounded-lg bg-brandPrimary/10 hover:bg-brandPrimary/20 border border-brandPrimary/30 hover:border-brandPrimary/50 transition-all duration-200"
                    title="Visit Portfolio"
                  >
                    <ExternalLink className="w-3 h-3 text-brandPrimary group-hover:text-brandSecondary transition-colors" />
                    <span className="text-xs font-medium text-brandPrimary group-hover:text-brandSecondary transition-colors">Portfolio</span>
                  </a>

                  <a
                    href="https://github.com/darshanbajgain"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="group flex items-center gap-1 px-2 py-1 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 transition-all duration-200"
                    title="View GitHub"
                  >
                    <Github className="w-3 h-3 text-gray-600 dark:text-gray-400 group-hover:text-gray-800 dark:group-hover:text-gray-200 transition-colors" />
                    <span className="text-xs font-medium text-gray-600 dark:text-gray-400 group-hover:text-gray-800 dark:group-hover:text-gray-200 transition-colors">GitHub</span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}

