import React from "react";
import Link from "next/link";
import { Github, Twitter, Mail, Heart, Code } from "lucide-react";
import Image from "next/image";

export function LandingFooter() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-backgroundSecondary border-t border-borderPrimary/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="flex flex-col md:flex-row items-center justify-between gap-6">
          {/* Brand section */}
          <div className="flex items-center gap-3">
            <Link
              href="/"
              className="flex items-center gap-2 relative z-10"
            >
              {/* <div className="w-8 h-8 lg:w-10 lg:h-10 rounded-xl bg-gradient-to-br from-brandPrimary to-brandSecondary flex items-center justify-center">
              <span className="text-white font-bold text-sm lg:text-base">
                P
              </span>
            </div> */}
              <Image src="/icon.svg" alt="logo" width={24} height={24} />
              <span className="font-bold text-xl  gradient-text">
                Profolify
              </span>
            </Link>
          </div>

          {/* Links */}
          <div className="flex items-center gap-6 text-sm">
            <Link href="#features" className="text-textSecondary hover:text-brandPrimary transition-colors duration-200">
              Features
            </Link>
            <Link href="#themes" className="text-textSecondary hover:text-brandPrimary transition-colors duration-200">
              Themes
            </Link>
            <Link href="#pricing" className="text-textSecondary hover:text-brandPrimary transition-colors duration-200">
              Pricing
            </Link>
          </div>

          {/* Social links */}
          <div className="flex items-center gap-3">
            <Link
              href="https://github.com/darshanbajgain"
              className="w-8 h-8 rounded-lg bg-backgroundPrimary/50 border border-borderPrimary/50 flex items-center justify-center text-textSecondary hover:text-brandPrimary hover:border-brandPrimary/50 transition-colors duration-200"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="GitHub"
            >
              <Github className="w-4 h-4" />
            </Link>
            <Link
              href="https://x.com/darshan_bajgain"
              className="w-8 h-8 rounded-lg bg-backgroundPrimary/50 border border-borderPrimary/50 flex items-center justify-center text-textSecondary hover:text-brandPrimary hover:border-brandPrimary/50 transition-colors duration-200"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="Twitter"
            >
              <Twitter className="w-4 h-4" />
            </Link>
            <Link
              href="mailto:<EMAIL>"
              className="w-8 h-8 rounded-lg bg-backgroundPrimary/50 border border-borderPrimary/50 flex items-center justify-center text-textSecondary hover:text-brandPrimary hover:border-brandPrimary/50 transition-colors duration-200"
              aria-label="Email"
            >
              <Mail className="w-4 h-4" />
            </Link>
          </div>
        </div>

        {/* Bottom section */}
        <div className="mt-8 pt-6 border-t border-borderPrimary/30 flex flex-col md:flex-row items-center justify-between gap-4 text-sm text-textSecondary">
          <p>© {currentYear} Profolify. All rights reserved.</p>

          {/* Enhanced Created by section */}
          <div className="flex items-center gap-2">
            <span className="flex items-center gap-2">
              <span className="text-textSecondary">Crafted with</span>
              <Heart className="w-4 h-4 text-red-500 animate-pulse" />
              <span className="text-textSecondary">by</span>
            </span>
            <a
              href="https://darshanbajgain.com.np/"
              target="_blank"
              rel="noopener noreferrer"
              className="group relative inline-flex items-center gap-2 px-3 py-1.5 rounded-lg bg-gradient-to-r from-brandPrimary/10 to-brandSecondary/10 border border-brandPrimary/20 hover:border-brandPrimary/40 transition-all duration-300 hover:shadow-lg hover:shadow-brandPrimary/20"
            >
              <Code className="w-4 h-4 text-brandPrimary group-hover:text-brandSecondary transition-colors duration-300" />
              <span className="font-semibold bg-gradient-to-r from-brandPrimary to-brandSecondary bg-clip-text text-transparent group-hover:from-brandSecondary group-hover:to-brandPrimary transition-all duration-300">
                Darshan Bajgain
              </span>
              <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-brandPrimary/5 to-brandSecondary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
}

